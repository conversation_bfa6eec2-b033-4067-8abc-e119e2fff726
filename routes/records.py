from dependencies import get_pipeline
from fastapi import APIRouter, Depends, BackgroundTasks, File, HTTPException, UploadFile, Form, Response
from fastapi.responses import RedirectResponse, HTMLResponse
from schemas import BiomarkerResponse, N1RecordUpdateRequest, RecordProgressUpdateRequest, QuestionResponse, RecordQueryParams, PaginatedRecordResponse, RecordCounts
from services import N1ProcessPipeline
from typing import Optional, List
import mimetypes
from io import BytesIO
from uuid import UUID, uuid4
import aiohttp
from datetime import datetime, timezone

router = APIRouter()

@router.post(
    "/cancel-batch",
    summary="Cancel a batch of records",
    response_model=dict)
async def cancel_record_batch(
    background_tasks: BackgroundTasks,
    user_id: UUID,
    batch_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Cancel a batch of records in the background:
    - Queues the batch cancellation task
    - Returns immediately with a status indicating the batch cancellation has been queued
    - The cancellation process will delete all records associated with the batch

    Parameters:
    - user_id: User identifier
    - batch_id: Batch identifier for the records to cancel

    Returns:
    - A dictionary with status information about the cancellation
    """
    try:
        # Add task to background tasks
        background_tasks.add_task(
            pipeline.cancel_batch,
            user_id,
            batch_id
        )
        return {
            "status": "success",
            "message": f"Batch {batch_id} cancellation has been queued",
            "batch_id": batch_id,
            "updated_at": datetime.now(timezone.utc)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error cancelling batch {batch_id} : {str(e)}"
        )

@router.post(
    "/process-batch",
    summary="Process a batch of records",
    response_model=dict)
async def process_record_batch(
    background_tasks: BackgroundTasks,
    user_id: UUID,
    batch_id: str,
    parser_type: str = "Sequential",
    parser_cloud: str = "Google",
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Process a batch of records in the background:
    - Queues the batch processing task
    - Returns immediately with a status indicating the batch has been queued

    Parameters:
    - user_id: User identifier
    - batch_id: Batch identifier for grouping records

    Returns:
    - A dictionary with batch_id, status, and updated_at timestamp
    """
    try:
        # TODO validate parser type to be only Sequential or Langroid and parser_cloud is Google or Amazon
        result = await pipeline.update_batch_user_records(user_id, batch_id, parser_type, parser_cloud, background_tasks)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing batch: {str(e)}"
        )
@router.post(
    "/reprocess-record",
    summary="Re-process a single record",
    response_model=dict)
async def reprocess_record(
    background_tasks: BackgroundTasks,
    user_id: UUID,
    record_id: str,
    parser_type: str = "Sequential",
    parser_cloud: str = "Google",
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Re-process a single record in the background:
    - Queues the re-processing task
    - Returns immediately with a status indicating the record reprocessing has been queued

    Parameters:
    - user_id: User identifier
    - record_id: identifier for selected record to be reprocessed

    Returns:
    - A dictionary with record_id, status, and updated_at timestamp
    """
    try:
        result = await pipeline.reprocess_single_records(user_id, record_id, parser_type, parser_cloud, background_tasks)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing batch: {str(e)}"
        )

@router.post(
    "/add",
    summary="Upload record file",
    response_model=N1RecordUpdateRequest)
async def add_new_record_file(
    background_tasks: BackgroundTasks,
    user_id: UUID = Form(..., description="User Identifier"),
    batch_id: str = Form(None, description="Batch Identifier"),
    file: UploadFile = File(..., description="File to upload"),
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Process a user's record(in background):
    - UserRecordRequest object is created and stored in the database.
    - File is uploaded to the Google Cloud Storage for downstream processing.
    - Pre Processing flow is triggered (TBD)
    """
    content = await file.read()
    file_object = BytesIO(content)
    # file_object.seek(0)  # Reset the file pointer to the beginning

    content_type = file.content_type or mimetypes.guess_type(file.filename or "")[0] or 'application/octet-stream'
    # Upload the file with content type
    record_id = f"mr-{str(uuid4())}"
    await pipeline.update_user_records(user_id, record_id, batch_id, file_object, file.filename or "unknown", content_type, background_tasks)

    return N1RecordUpdateRequest(id=record_id, user_id=user_id, status="PENDING", file_name=file.filename)

@router.post(
    "/reprocess",
    summary="Reprocess existing records or batch",
    response_model=dict)
async def reprocess_existing_records_by_batch(
    background_tasks: BackgroundTasks,
    user_id: UUID,
    batch_id: Optional[str] = None,
    parser_type: str = "Sequential",
    parser_cloud: str = "Google",
    resume: bool = True,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Process a user's existing records optionally within a batch (in background):
    - If resume=True: Continue processing only incomplete records
    - If resume=False: cleanup and reprocess the records from start
    - If batch_id is provided, process all records with in the batch
    """
    try:
        result = await pipeline.update_existing_user_records(user_id, batch_id, parser_type, parser_cloud, resume, background_tasks)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing batch: {str(e)}"
        )

@router.get("/status",
    summary="Check status of uploaded record",
    response_model=N1RecordUpdateRequest)
async def get_record_status(
    user_id: UUID,
    record_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Return the status of the record for a given user_id and record_id.
    """
    return pipeline.get_record_status(user_id, record_id)

@router.get("/file",
    summary="Get the signed url of the uploaded record")
async def get_record_file(
    user_id: UUID,
    record_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Redirects to the url of the uploaded record file for a given user_id and record_id.
    """
    resp = pipeline.get_record_status(user_id, record_id) # throws 404 if not found
    return RedirectResponse(pipeline.get_file_url(user_id, record_id, "record", resp.file_name))

@router.post("/status",
    summary="Update status of uploaded record",
    response_model=N1RecordUpdateRequest)
async def update_record_status(
    update_request: RecordProgressUpdateRequest,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Update the status and progress of the record for a given user_id and record_id.

    Parameters:
    - update_request: Contains user_id, record_id, progress (0-100), status, and optional error message

    Returns:
    - Updated record information
    """
    # Use the service method to update the record progress
    updated_record = await pipeline.update_record_progress(update_request)
    return updated_record

@router.get("/data/markdown", response_model=str, deprecated=True,
    summary="Get the markdown content of the uploaded record")
async def get_markdown_content(
    user_id: UUID,
    record_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Returns the markdown content of the record for a given user_id and record_id.
    """

    raise HTTPException(status_code=404, detail="Markdown records are not supported now.")

@router.get("/user-biomarkers", summary="Get all biomarkers for a user", response_model=BiomarkerResponse, deprecated=True)
async def get_user_biomarkers_old(
    user_id: UUID,
    record_id: Optional[str] = None,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)

):
    """
        This endpoint returns all the biomarkers for a given user identified by user_id and optionally filtered by record_id
    """
    try:
        result = await pipeline.get_user_biomarkers_response(user_id, record_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching records: {str(e)}"
        )





@router.post(
    "/sync-biomarkers/{user_id}/{record_id}",
    summary="Sync biomarkers for a completed record to Bubble API",
    response_model=dict,
)
async def sync_user_biomarkers_by_record_id(
    user_id: UUID,
    record_id: str,
    background_tasks: BackgroundTasks,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Check if a record has progress >=95, meaning biomarkers were extracted and saved to DB.
    If so, queue biomarkers sync and update the status to progress 100 and status 'COMPLETED' on success.
    """
    try:
        # Get record status
        record_status = pipeline.get_record_status(user_id, record_id)
        background_tasks.add_task(
            pipeline.sync_biomarkers_to_bubble,
            user_id,
            record_id
        )

        return {
            "status": "success",
            "message": f"Biomarker sync for record {record_id} has been queued and record marked as COMPLETED",
            "record": {
                "id": record_status.id,
                "status": record_status.status,
                "progress": record_status.progress
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error queuing biomarker sync: {str(e)}"
        )

@router.delete(
    "/{user_id}/{record_id}",
    summary="Delete a record and all associated clinical data",
    response_model=dict
)
async def delete_record_by_id(
    user_id: UUID,
    record_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Delete a record and all associated clinical data:
    - Deletes the record from the record_requests table
    - Deletes all associated clinical data from the clinical_data table
    - Returns a response indicating success or failure
    """
    return pipeline.delete_record(user_id, record_id)





@router.get(
    "/loinc/{loinc_num}",
    summary="Get a LOINC record by its code",
    response_model=LoincRecord
)
async def get_loinc_by_loinc_num(
    loinc_num: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get a LOINC record by its code:
    - Finds the LOINC record by its code
    - Returns the LOINC record details

    Parameters:
    - loinc_num: The LOINC code to look up

    Returns:
    - LOINC record details
    """
    return pipeline.get_loinc_record(loinc_num)

@router.get(
    "/user/{user_id}",
    summary="Get all records for a user",
    response_model=List[N1RecordUpdateRequest],
    deprecated=True
)
async def get_user_records(
    user_id: UUID,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get all records for a specific user:
    - Retrieves all records associated with the given user_id
    - Returns a list of record objects with their status and metadata

    Parameters:
    - user_id: User identifier

    Returns:
    - List of record objects for the specified user
    """
    try:
        records = await pipeline.get_user_records(user_id)
        return records
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching user records: {str(e)}"
        )


@router.get("/paginated", response_model=PaginatedRecordResponse)
async def get_user_records_paginated(
    query_params: RecordQueryParams = Depends(),
    pipeline: N1ProcessPipeline = Depends(get_pipeline),
):
    """
    Get paginated, sorted, and filtered records for a specific user.

    This endpoint provides advanced querying capabilities including:
    - Pagination with configurable page sizes
    - Sorting by any record field (status, created_at, updated_at, progress, etc.)
    - Filtering by record ID, status, type, filename, batch ID, progress ranges, and date ranges

    Example usage:
    - Get first 10 records: ?page=1&page_size=10
    - Sort by created_at ascending: ?sort_by=created_at&is_descending=false
    - Sort by updated_at descending: ?sort_by=updated_at&is_descending=true
    - Filter by status: ?status=COMPLETED
    - Filter by type: ?type=RECORD
    - Filter by progress range: ?progress_from=50&progress_to=100
    - Filter by date range: ?created_from=2023-01-01&created_to=2023-12-31
    - Filter by batch ID: ?batch_id=batch123
    - Search by filename: ?file_name=report.pdf
    """
    return await pipeline.get_user_records_paginated(query_params)

@router.post(
    "/question-response",
    summary="Save a health question-response",
    response_model=QuestionResponse
)
async def save_user_question_response(
    question: QuestionResponse,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Save a health question-response from the user:
    - Processes and stores health question data
    - Returns the saved question with its ID and timestamps

    The question includes:
    - question: The health question being answered
    - answer: The user's response
    - created_date: When the question was filled out (millisecond timestamp)
    - related_date: Optional reference date for the question (millisecond timestamp)
    - user_id: User identifier
    - category: Category of the question (e.g., nutrition, exercise)
    """
    try:
        return await pipeline.save_question_response(question)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error saving question: {str(e)}"
        )

@router.post("/enrich-record",
    summary="Update status of uploaded record",
    response_model=N1RecordUpdateRequest)
async def enrich_record(
    question: QuestionResponse,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Save a health question-response from the user:
    - Processes and stores health question data
    - Returns the saved question with its ID and timestamps

    The question includes:
    - question: The health question being answered
    - answer: The user's response
    - created_date: When the question was filled out (millisecond timestamp)
    - related_date: Optional reference date for the question (millisecond timestamp)
    - user_id: User identifier
    - category: Category of the question (e.g., nutrition, exercise)
    """
    try:
        return await pipeline.save_question_response(question)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error saving question: {str(e)}"
        )

@router.get(
    "/counts/{record_id}",
    summary="Get counts of biomarkers, diagnosis, genetics, and procedures for a record",
    response_model=RecordCounts
)
async def get_record_counts(
    record_id: str,
    pipeline: N1ProcessPipeline = Depends(get_pipeline)
):
    """
    Get counts of biomarkers, diagnosis, genetics, and procedures for a specific record.

    Parameters:
    - record_id: Record identifier

    Returns:
    - RecordCounts object containing counts for:
      - biomarkers: Number of biomarker records
      - diagnosis: Number of diagnosis records
      - genetics: Number of genetics records
      - procedures: Number of procedure records
    """
    try:
        return await pipeline.get_record_counts(record_id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching record counts: {str(e)}"
        )